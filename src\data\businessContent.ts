// Business content for Oklahoma MSP
export const businessContent = {
  company: {
    name: "SoonerTech Solutions",
    tagline: "Securing Oklahoma's Digital Future",
    location: "Oklahoma City, OK",
    established: "2019"
  },

  welcome: {
    title: "CONNECTING TO SOONERTECH_SYSTEMS...",
    subtitle: "Oklahoma’s Premier Managed IT Provider",
    intro: [
      "Establishing encrypted connection...",
      "Authentication successful.",
      "Welcome to SoonerTech Solutions.",
      "",
      "Your trusted technology partner in the heart of Oklahoma.",
      "Proudly serving OKC, Tulsa, and surrounding regions.",
      "",
      "System Status: OPERATIONAL",
      "Security Level: MAXIMUM",
      "Support Coverage: 24/7/365"
    ]
  },

  services: {
    title: "AVAILABLE_SERVICES.EXE",
    description: "End-to-end IT solutions tailored for Oklahoma businesses",
    offerings: [
      {
        id: "managed_it",
        name: "Managed IT Services",
        description: "Fully managed IT infrastructure with proactive support",
        details: [
          "24/7 network monitoring and real-time support",
          "Routine maintenance and patch management",
          "Help desk for users across all skill levels",
          "Strategic roadmapping and IT lifecycle planning"
        ]
      },
      {
        id: "cybersecurity",
        name: "Cybersecurity Solutions",
        description: "Layered security and regulatory compliance protection",
        details: [
          "Enterprise-grade threat detection and remediation",
          "Firewalls, endpoint protection, and SOC monitoring",
          "Employee security training and phishing simulation",
          "Compliance auditing (HIPAA, PCI, SOX, and more)"
        ]
      },
      {
        id: "cloud_services",
        name: "Cloud Services",
        description: "Cloud enablement, migration, and optimization",
        details: [
          "Seamless cloud migration services",
          "Microsoft 365, Azure, and hybrid cloud support",
          "Secure backup and disaster recovery",
          "Cost-efficient scaling and performance tuning"
        ]
      },
      {
        id: "business_continuity",
        name: "Business Continuity",
        description: "Preparedness solutions to keep operations running",
        details: [
          "Custom disaster recovery planning",
          "Automated backups and data integrity checks",
          "Business impact analysis and mitigation planning",
          "Emergency protocols and failover systems"
        ]
      }
    ]
  },

  support: {
    title: "SUPPORT_PORTAL.ACCESS",
    description: "Oklahoma-based support you can count on",
    contact: {
      phone: "(405) 555-TECH",
      email: "<EMAIL>",
      emergency: "(405) 555-911",
      hours: "Emergency Support: 24/7/365"
    },
    locations: [
      {
        city: "Oklahoma City",
        address: "123 Technology Drive, OKC, OK 73102",
        phone: "(405) 555-TECH"
      },
      {
        city: "Tulsa",
        address: "456 Innovation Blvd, Tulsa, OK 74103",
        phone: "(918) 555-TECH"
      }
    ]
  },

  about: {
    title: "COMPANY_INFO.DAT",
    description: "Inside SoonerTech Solutions",
    story: [
      "Founded in 2019 by Oklahoma natives who understand",
      "the evolving technology needs of local businesses.",
      "",
      "At SoonerTech, we blend enterprise-grade expertise",
      "with genuine Oklahoma hospitality.",
      "",
      "Our certified engineers bring over 50 years of",
      "combined experience in IT infrastructure and security.",
      "",
      "From Main Street offices in Norman to enterprise data",
      "centers in downtown OKC, we’re your go-to tech team."
    ],
    stats: [
      { label: "Clients Served", value: "200+" },
      { label: "Uptime Guarantee", value: "99.9%" },
      { label: "Avg. Response Time", value: "<15 Minutes" },
      { label: "Team Experience", value: "50+ Years" }
    ],
    certifications: [
      "Microsoft Gold Partner",
      "CompTIA Authorized Partner",
      "Cisco Select Partner",
      "VMware Professional Partner"
    ]
  },

  navigation: {
    main: [
      { id: "services", label: "Services & Solutions", path: "/services" },
      { id: "support", label: "Support Portal", path: "/support" },
      { id: "about", label: "About Us", path: "/about" }
    ]
  }
};

export default businessContent;